#!/bin/bash

echo "🌐 Starting Krubu Frontend (Static Mode)"
echo "========================================"

# Build CSS if needed
if [ ! -f "public/static/styles.css" ] || [ ! -s "public/static/styles.css" ]; then
    echo "🎨 Building Tailwind CSS..."
    npm run build
fi

echo ""
echo "✅ Starting simple HTTP server..."
echo "   Frontend only - no backend functionality"
echo "   Perfect for testing UI/UX changes"
echo ""
echo "🔑 Test Credentials (won't work in static mode):"
echo "   Email: <EMAIL>"
echo "   Password: test123"
echo ""
echo "🌐 App will be available at:"
echo "   http://localhost:8000/login.html"
echo ""
echo "📱 What works in static mode:"
echo "   ✅ UI layout and styling"
echo "   ✅ Tab switching"
echo "   ✅ Form validation"
echo "   ✅ Copy to clipboard"
echo "   ✅ Download markdown"
echo "   ✅ Authentication redirect logic"
echo ""
echo "❌ What doesn't work (requires backend):"
echo "   ❌ Actual login/logout"
echo "   ❌ Query submission"
echo "   ❌ History loading"
echo "   ❌ Payment integration"
echo ""
echo "💡 For full functionality, use: ./start_local.sh"
echo ""
echo "🚀 Starting server on port 8000..."
echo "========================================"

# Start simple HTTP server
python3 -m http.server 8000 --directory public
