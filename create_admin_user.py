#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create admin user for testing the migrated frontend
"""

import sqlite3
import bcrypt
from datetime import datetime

# Use existing user credentials for testing
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"  # We'll need to verify this or reset it

def update_database_schema():
    """Update database schema to match Netlify functions expectations"""
    conn = sqlite3.connect('purpose_robot.db')
    c = conn.cursor()

    try:
        # Add missing columns to Users table
        c.execute("ALTER TABLE Users ADD COLUMN query_count INTEGER DEFAULT 0")
        print("✅ Added query_count column")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ query_count column already exists")
        else:
            print(f"❌ Error adding query_count: {e}")

    try:
        c.execute("ALTER TABLE Users ADD COLUMN last_reset TEXT")
        print("✅ Added last_reset column")
        # Update existing rows with current timestamp
        c.execute("UPDATE Users SET last_reset = ? WHERE last_reset IS NULL", (datetime.now().isoformat(),))
        print("✅ Updated existing users with current timestamp")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("✅ last_reset column already exists")
        else:
            print(f"❌ Error adding last_reset: {e}")

    conn.commit()
    conn.close()

def create_admin_user():
    """Update existing user to admin status and create test credentials"""

    # First update schema
    update_database_schema()

    # Connect to database
    conn = sqlite3.connect('purpose_robot.db')
    c = conn.cursor()

    # Get existing users
    c.execute("SELECT id, email, tier FROM Users")
    users = c.fetchall()

    print("\n📋 Existing users:")
    for user in users:
        print(f"  ID: {user[0]}, Email: {user[1]}, Tier: {user[2]}")

    # Update first user to PhD tier for testing
    if users:
        user_id = users[0][0]
        user_email = users[0][1]

        c.execute("""
            UPDATE Users
            SET tier = 'phd', query_count = 0, last_reset = ?
            WHERE id = ?
        """, (datetime.now().isoformat(), user_id))

        conn.commit()

        print(f"\n🎉 Updated user for testing!")
        print(f"📧 Email: {user_email}")
        print(f"🔑 Password: Try 'password123' or 'test123' or check with the original creator")
        print(f"🎓 Tier: PhD (Unlimited queries)")
        print(f"🆔 User ID: {user_id}")

        global TEST_EMAIL
        TEST_EMAIL = user_email

        # Create some sample reports for testing
        sample_reports = [
            {
                "prompt": "Simulate the impact of carbon tax on automotive industry",
                "response": "A carbon tax would significantly impact the automotive industry by increasing production costs for traditional vehicles while accelerating the transition to electric vehicles. Key impacts include: 1) Increased manufacturing costs for ICE vehicles, 2) Accelerated R&D investment in EVs, 3) Supply chain restructuring, 4) Consumer behavior shift towards cleaner alternatives."
            },
            {
                "prompt": "Simulate the impact of Universal Basic Income on economic growth",
                "response": "Universal Basic Income implementation would have mixed effects on economic growth. Positive impacts include increased consumer spending, reduced poverty, and enhanced entrepreneurship. Negative impacts may include inflation pressure, reduced work incentives for some demographics, and significant fiscal burden requiring tax restructuring."
            },
            {
                "prompt": "Simulate the impact of AI regulation on tech industry innovation",
                "response": "Comprehensive AI regulation would reshape tech industry innovation patterns. While it may slow certain developments in the short term, it would likely drive innovation in AI safety, transparency, and ethical AI systems. Companies would need to invest more in compliance infrastructure but could benefit from increased consumer trust and standardized frameworks."
            }
        ]

        # Check if reports already exist for this user
        c.execute("SELECT COUNT(*) FROM Reports WHERE user_id = ?", (user_id,))
        existing_reports = c.fetchone()[0]

        if existing_reports == 0:
            for report in sample_reports:
                c.execute("""
                    INSERT INTO Reports (user_id, prompt, response, tier, timestamp)
                    VALUES (?, ?, ?, ?, ?)
                """, (user_id, report["prompt"], report["response"], "phd", datetime.now().isoformat()))

            conn.commit()
            print(f"📊 Created {len(sample_reports)} sample reports for testing")
        else:
            print(f"📊 User already has {existing_reports} reports")

    conn.close()

def check_database_structure():
    """Check and display database structure"""
    conn = sqlite3.connect('purpose_robot.db')
    c = conn.cursor()

    # Check Users table
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Users'")
    if c.fetchone():
        c.execute("PRAGMA table_info(Users)")
        users_columns = c.fetchall()
        print("\n📋 Users table structure:")
        for col in users_columns:
            print(f"  - {col[1]} ({col[2]})")

        c.execute("SELECT COUNT(*) FROM Users")
        user_count = c.fetchone()[0]
        print(f"👥 Total users: {user_count}")
    else:
        print("❌ Users table not found")

    # Check Reports table
    c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Reports'")
    if c.fetchone():
        c.execute("PRAGMA table_info(Reports)")
        reports_columns = c.fetchall()
        print("\n📋 Reports table structure:")
        for col in reports_columns:
            print(f"  - {col[1]} ({col[2]})")

        c.execute("SELECT COUNT(*) FROM Reports")
        report_count = c.fetchone()[0]
        print(f"📊 Total reports: {report_count}")
    else:
        print("❌ Reports table not found")

    conn.close()

if __name__ == "__main__":
    print("🚀 Creating admin user for Krubu frontend testing...")
    print("=" * 50)

    # Check database structure first
    check_database_structure()

    print("\n" + "=" * 50)

    # Create admin user
    create_admin_user()

    print("\n" + "=" * 50)
    print("✨ Setup complete! You can now test the frontend with these credentials:")
    print(f"   Email: {TEST_EMAIL}")
    print(f"   Password: {TEST_PASSWORD} (or try common passwords)")
    print("\n🌐 To test locally:")
    print("   1. Start Netlify Dev: netlify dev")
    print("   2. Or use Python server: python3 -m http.server 8000 --directory public")
    print("   3. Open: http://localhost:8000/login.html")
    print("   4. Login with the credentials above")
    print("\n🔧 If password doesn't work, you can reset it by running:")
    print("   python3 -c \"import sqlite3, bcrypt; conn=sqlite3.connect('purpose_robot.db'); c=conn.cursor(); c.execute('UPDATE Users SET password_hash=? WHERE email=?', (bcrypt.hashpw(b'test123', bcrypt.gensalt()).decode(), '<EMAIL>')); conn.commit(); print('Password reset to: test123')\"")
    print("\n📱 Test the new features:")
    print("   - Sidebar navigation with simulation history")
    print("   - Tabbed interface (Summary, Impact Chart, Timeline, Sources)")
    print("   - Copy and download functionality")
    print("   - Premium unlock features")
