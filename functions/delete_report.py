import json
from utils import get_user_from_token
from db_utils import delete_user_report

def handler(event, context):
    """Delete user report"""
    
    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'DELETE, OPTIONS'
    }
    
    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }
    
    if event['httpMethod'] != 'DELETE':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({'error': 'Method not allowed'})
        }
    
    # Get user from token
    auth_header = event.get('headers', {}).get('authorization', '')
    token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    user = get_user_from_token(token)
    
    if not user:
        return {
            'statusCode': 401,
            'headers': headers,
            'body': json.dumps({'error': 'Authentication required'})
        }
    
    try:
        # Get report ID from path
        path_params = event.get('pathParameters', {})
        report_id = path_params.get('id')
        
        if not report_id:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Report ID required'})
            }
        
        # Delete report
        deleted = delete_user_report(user['id'], int(report_id))
        
        if not deleted:
            return {
                'statusCode': 404,
                'headers': headers,
                'body': json.dumps({'error': 'Report not found or not authorized'})
            }
        
        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps({'message': 'Report deleted successfully'})
        }
        
    except ValueError:
        return {
            'statusCode': 400,
            'headers': headers,
            'body': json.dumps({'error': 'Invalid report ID'})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': str(e)})
        }
