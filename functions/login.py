import json
import os
from utils import verify_password, create_jwt_token

# Use local SQLite database for local development
try:
    if os.environ.get('SUPABASE_URL') or os.environ.get('SUPABASE_HOST'):
        from db_utils import get_user_by_email
    else:
        raise ImportError("Using local database")
except ImportError:
    from db_utils_local import get_user_by_email

def handler(event, context):
    """User login handler"""

    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    }

    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }

    if event['httpMethod'] != 'POST':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({'error': 'Method not allowed'})
        }

    try:
        body = json.loads(event['body'])
        email = body.get('email', '').strip().lower()
        password = body.get('password', '')

        # Validate input
        if not email or not password:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Email and password are required'})
            }

        # Get user by email
        user = get_user_by_email(email)
        if not user:
            return {
                'statusCode': 401,
                'headers': headers,
                'body': json.dumps({'error': 'Invalid email or password'})
            }

        # Verify password
        if not verify_password(password, user['password_hash']):
            return {
                'statusCode': 401,
                'headers': headers,
                'body': json.dumps({'error': 'Invalid email or password'})
            }

        # Create JWT token
        token = create_jwt_token(user['id'])

        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps({
                'message': 'Login successful',
                'token': token,
                'user': {
                    'id': user['id'],
                    'email': user['email'],
                    'tier': user['tier'],
                    'query_count': user['query_count']
                }
            })
        }

    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': headers,
            'body': json.dumps({'error': 'Invalid JSON'})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': str(e)})
        }
