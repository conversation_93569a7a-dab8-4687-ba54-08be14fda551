import json
import os
import razorpay
from utils import get_user_from_token

# Razorpay setup
RAZ<PERSON><PERSON>Y_KEY_ID = os.environ.get("RAZORPAY_KEY_ID")
RAZORPAY_KEY_SECRET = os.environ.get("RAZORPAY_KEY_SECRET")
razorpay_client = razorpay.Client(auth=(RAZORPAY_KEY_ID, RAZOR<PERSON>Y_KEY_SECRET))

# Tier prices in paise (INR)
TIER_PRICES = {
    'graduate': 50000,  # ₹500
    'masters': 100000,  # ₹1000
    'phd': 200000       # ₹2000
}

def handler(event, context):
    """Create Razorpay order for tier upgrade"""
    
    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    }
    
    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }
    
    if event['httpMethod'] != 'POST':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({'error': 'Method not allowed'})
        }
    
    # Get user from token
    auth_header = event.get('headers', {}).get('authorization', '')
    token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    user = get_user_from_token(token)
    
    if not user:
        return {
            'statusCode': 401,
            'headers': headers,
            'body': json.dumps({'error': 'Authentication required'})
        }
    
    try:
        body = json.loads(event['body'])
        tier = body.get('tier')
        
        # Validate tier
        if tier not in ['graduate', 'masters', 'phd']:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Invalid tier selected'})
            }
        
        amount = TIER_PRICES[tier]
        
        # Create Razorpay order
        order = razorpay_client.order.create({
            "amount": amount,
            "currency": "INR",
            "receipt": f"receipt_{user['id']}_{tier}",
            "notes": {"user_id": str(user['id']), "tier": tier}
        })
        
        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps({
                'order_id': order['id'],
                'amount': amount,
                'currency': 'INR',
                'key_id': RAZORPAY_KEY_ID,
                'tier': tier
            })
        }
        
    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': headers,
            'body': json.dumps({'error': 'Invalid JSON'})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': f'Failed to create order: {str(e)}'})
        }
