import json
import os
from utils import get_user_from_token

# Use local SQLite database for local development
try:
    if os.environ.get('SUPABASE_URL') or os.environ.get('SUPABASE_HOST'):
        from db_utils import get_user_reports
    else:
        raise ImportError("Using local database")
except ImportError:
    from db_utils_local import get_user_reports

def handler(event, context):
    """Get user chat history"""

    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
    }

    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }

    if event['httpMethod'] != 'GET':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({'error': 'Method not allowed'})
        }

    # Get user from token
    auth_header = event.get('headers', {}).get('authorization', '')
    token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    user = get_user_from_token(token)

    if not user:
        return {
            'statusCode': 401,
            'headers': headers,
            'body': json.dumps({'error': 'Authentication required'})
        }

    try:
        # Get user reports
        reports = get_user_reports(user['id'])

        # Format reports for frontend
        history = [
            {
                'id': report['id'],
                'query': report['prompt'],
                'response': report['response']
            }
            for report in reports
        ]

        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps(history)
        }

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': str(e)})
        }
