import json
import os
from utils import get_user_from_token, check_and_increment_quota, query_openai, TIER_CONFIG
from db_utils import save_report

def handler(event, context):
    """Main index handler for policy simulation"""
    
    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    }
    
    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }
    
    # Get user from token
    auth_header = event.get('headers', {}).get('authorization', '')
    token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    user = get_user_from_token(token)
    
    if not user:
        return {
            'statusCode': 401,
            'headers': headers,
            'body': json.dumps({'error': 'Authentication required'})
        }
    
    if event['httpMethod'] == 'GET':
        # Return user info and tier config
        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps({
                'user': {
                    'id': user['id'],
                    'email': user['email'],
                    'tier': user['tier'],
                    'query_count': user['query_count']
                },
                'tier_config': TIER_CONFIG
            })
        }
    
    elif event['httpMethod'] == 'POST':
        try:
            body = json.loads(event['body'])
            query = body.get('query', '').strip()
            tier = body.get('tier', user['tier'])
            
            # Validate tier access
            if tier != user['tier']:
                return {
                    'statusCode': 403,
                    'headers': headers,
                    'body': json.dumps({'error': 'Tier upgrade required'})
                }
            
            # Check quota
            can_query, quota_message = check_and_increment_quota(user)
            if not can_query:
                return {
                    'statusCode': 429,
                    'headers': headers,
                    'body': json.dumps({'error': quota_message})
                }
            
            # Validate query format
            if " on " not in query:
                return {
                    'statusCode': 400,
                    'headers': headers,
                    'body': json.dumps({
                        'error': "Please use format 'Simulate the impact of [policy] on [sector]'"
                    })
                }
            
            # Parse query
            parts = query.split(" on ")
            policy = parts[0].replace("Simulate the impact of ", "").strip()
            sector = parts[1].strip()
            
            # Generate report
            report = query_openai(policy, sector, tier)
            
            # Save report
            save_report(user['id'], query, report, tier)
            
            return {
                'statusCode': 200,
                'headers': headers,
                'body': json.dumps({
                    'report': report,
                    'query': query,
                    'tier': tier
                })
            }
            
        except json.JSONDecodeError:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Invalid JSON'})
            }
        except Exception as e:
            return {
                'statusCode': 500,
                'headers': headers,
                'body': json.dumps({'error': str(e)})
            }
    
    return {
        'statusCode': 405,
        'headers': headers,
        'body': json.dumps({'error': 'Method not allowed'})
    }
