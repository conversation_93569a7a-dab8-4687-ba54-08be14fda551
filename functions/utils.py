import os
import openai
import bcrypt
import jwt
from datetime import datetime, timedelta
from db_utils import get_user_by_id, reset_query_count, increment_query_count

# OpenAI setup
openai.api_key = os.environ.get("OPENAI_API_KEY")

# JWT secret for session management
JWT_SECRET = os.environ.get("JWT_SECRET", "your-jwt-secret")

# Tier configurations
TIER_CONFIG = {
    'free': {'model': 'gpt-3.5-turbo', 'quota': 5, 'max_tokens': 300},
    'graduate': {'model': 'gpt-3.5-turbo', 'quota': 20, 'max_tokens': 500},
    'masters': {'model': 'gpt-4-turbo', 'quota': 50, 'max_tokens': 1000},
    'phd': {'model': 'gpt-4-turbo', 'quota': float('inf'), 'max_tokens': 2000}
}

# Cost settings
COST_PER_1K_INPUT = 0.0005
COST_PER_1K_OUTPUT = 0.0015
MAX_COST_PER_QUERY = 0.001

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

def verify_password(password, hashed):
    """Verify password against hash"""
    if isinstance(hashed, str):
        hashed = hashed.encode('utf-8')
    return bcrypt.checkpw(password.encode('utf-8'), hashed)

def create_jwt_token(user_id):
    """Create JWT token for user session"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(days=7)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def verify_jwt_token(token):
    """Verify JWT token and return user_id"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return payload['user_id']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def get_user_from_token(token):
    """Get user from JWT token"""
    if not token:
        return None
    user_id = verify_jwt_token(token)
    if not user_id:
        return None
    return get_user_by_id(user_id)

def reset_query_count_if_needed(user):
    """Reset query count if needed"""
    last_reset = user['last_reset']
    now = datetime.now()
    if now.date() > last_reset.date():
        reset_query_count(user['id'])
        user['query_count'] = 0
        user['last_reset'] = now
    return user

def check_and_increment_quota(user):
    """Check and increment query quota"""
    user = reset_query_count_if_needed(user)
    tier_quota = TIER_CONFIG[user['tier']]['quota']
    if user['tier'] != 'phd' and user['query_count'] >= tier_quota:
        return False, f"Quota exceeded for {user['tier']} tier ({tier_quota} queries/day). Upgrade your tier."
    increment_query_count(user['id'])
    user['query_count'] += 1
    return True, None

def estimate_cost(prompt_tokens, completion_tokens):
    """Estimate cost from tokens"""
    input_cost = (prompt_tokens / 1000) * COST_PER_1K_INPUT
    output_cost = (completion_tokens / 1000) * COST_PER_1K_OUTPUT
    return input_cost + output_cost

def query_openai(policy, sector, tier):
    """Query OpenAI API with tier-based prompts"""
    base_prompt = f"""
You are Purpose Robot, a policy impact simulator. Simulate the potential impacts of a policy on a specific sector.
User query: "Simulate the impact of {policy} on {sector}"
- Focus ONLY on predicting impacts: economic (costs, benefits, etc.), social (public perception, equity, etc.), and legal (regulations, risks, etc.).
- Be concise, no extra context or policy description.
"""
    if tier == 'free':
        prompt = base_prompt + """
- Provide a basic summary with one short sentence per impact.
- Format:
Economic: [Basic economic impact]
Social: [Basic social impact]
Legal: [Basic legal impact]
"""
    elif tier == 'graduate':
        prompt = base_prompt + """
- Provide deeper analysis with two sentences per impact, including light contextual reasoning.
- Format:
Economic: [Detailed economic impact]. [Reasoning]
Social: [Detailed social impact]. [Reasoning]
Legal: [Detailed legal impact]. [Reasoning]
"""
    elif tier == 'masters':
        prompt = base_prompt + """
- Provide multi-source synthesis with three sentences per impact, integrating hypothetical data or trends.
- Format:
Economic: [Detailed economic impact]. [Reasoning]. [Hypothetical data/trend]
Social: [Detailed social impact]. [Reasoning]. [Hypothetical data/trend]
Legal: [Detailed legal impact]. [Reasoning]. [Hypothetical data/trend]
"""
    elif tier == 'phd':
        prompt = base_prompt + """
- Provide policy-grade briefings with four sentences per impact, including actionable insights.
- Format:
Economic: [Detailed economic impact]. [Reasoning]. [Hypothetical data/trend]. [Actionable insight]
Social: [Detailed social impact]. [Reasoning]. [Hypothetical data/trend]. [Actionable insight]
Legal: [Detailed legal impact]. [Reasoning]. [Hypothetical data/trend]. [Actionable insight]
"""
    
    est_input_tokens = len(prompt) // 4 + len(policy + sector) // 4
    est_cost = estimate_cost(est_input_tokens, TIER_CONFIG[tier]['max_tokens'])
    if est_cost > MAX_COST_PER_QUERY:
        return f"Error: Query too costly (estimated ${est_cost:.5f} exceeds ${MAX_COST_PER_QUERY})"
    
    try:
        response = openai.ChatCompletion.create(
            model=TIER_CONFIG[tier]['model'],
            messages=[
                {"role": "system", "content": prompt},
                {"role": "user", "content": f"Simulate the impact of {policy} on {sector}"}
            ],
            max_tokens=TIER_CONFIG[tier]['max_tokens'],
            temperature=0.7
        )
        report = response.choices[0].message.content
        actual_cost = estimate_cost(response.usage.prompt_tokens, response.usage.completion_tokens)
        if actual_cost > MAX_COST_PER_QUERY:
            return f"Error: Query cost (${actual_cost:.5f}) exceeds limit (${MAX_COST_PER_QUERY})"
        return report
    except Exception as e:
        return f"Error: API call failed - {str(e)}"
