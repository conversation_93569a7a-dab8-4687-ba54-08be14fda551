import os
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

def get_db_connection():
    """Get database connection using Supabase PostgreSQL"""
    # Parse Supabase URL if provided, otherwise use individual components
    supabase_url = os.environ.get('SUPABASE_URL')
    if supabase_url:
        return psycopg2.connect(supabase_url)
    else:
        return psycopg2.connect(
            host=os.environ.get('SUPABASE_HOST'),
            database=os.environ.get('SUPABASE_DB'),
            user=os.environ.get('SUPABASE_USER'),
            password=os.environ.get('SUPABASE_PASSWORD'),
            port=os.environ.get('SUPABASE_PORT', '5432')
        )

def init_db():
    """Initialize database tables"""
    conn = get_db_connection()
    cur = conn.cursor()

    # Create Users table
    cur.execute('''
        CREATE TABLE IF NOT EXISTS Users (
            id SERIAL PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            tier VARCHAR(50) DEFAULT 'free',
            query_count INTEGER DEFAULT 0,
            last_reset TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Create Reports table
    cur.execute('''
        CREATE TABLE IF NOT EXISTS Reports (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES Users(id),
            prompt TEXT,
            response TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tier VARCHAR(50)
        )
    ''')

    conn.commit()
    cur.close()
    conn.close()

def get_user_by_id(user_id):
    """Get user by ID"""
    conn = get_db_connection()
    cur = conn.cursor(cursor_factory=RealDictCursor)
    cur.execute("SELECT * FROM Users WHERE id = %s", (user_id,))
    user = cur.fetchone()
    cur.close()
    conn.close()
    return user

def get_user_by_email(email):
    """Get user by email"""
    conn = get_db_connection()
    cur = conn.cursor(cursor_factory=RealDictCursor)
    cur.execute("SELECT * FROM Users WHERE email = %s", (email,))
    user = cur.fetchone()
    cur.close()
    conn.close()
    return user

def create_user(email, password_hash):
    """Create new user"""
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute(
            "INSERT INTO Users (email, password_hash) VALUES (%s, %s) RETURNING id",
            (email, password_hash)
        )
        user_id = cur.fetchone()[0]
        conn.commit()
        return user_id
    except psycopg2.IntegrityError:
        conn.rollback()
        return None
    finally:
        cur.close()
        conn.close()

def update_user_tier(user_id, tier):
    """Update user tier"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("UPDATE Users SET tier = %s WHERE id = %s", (tier, user_id))
    conn.commit()
    cur.close()
    conn.close()

def reset_query_count(user_id):
    """Reset user query count"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute(
        "UPDATE Users SET query_count = 0, last_reset = %s WHERE id = %s",
        (datetime.now(), user_id)
    )
    conn.commit()
    cur.close()
    conn.close()

def increment_query_count(user_id):
    """Increment user query count"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("UPDATE Users SET query_count = query_count + 1 WHERE id = %s", (user_id,))
    conn.commit()
    cur.close()
    conn.close()

def save_report(user_id, prompt, response, tier):
    """Save report to database"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute(
        "INSERT INTO Reports (user_id, prompt, response, tier) VALUES (%s, %s, %s, %s)",
        (user_id, prompt, response, tier)
    )
    conn.commit()
    cur.close()
    conn.close()

def get_user_reports(user_id):
    """Get user reports"""
    conn = get_db_connection()
    cur = conn.cursor(cursor_factory=RealDictCursor)
    cur.execute(
        "SELECT id, prompt, response FROM Reports WHERE user_id = %s ORDER BY timestamp DESC",
        (user_id,)
    )
    reports = cur.fetchall()
    cur.close()
    conn.close()
    return reports

def delete_user_report(user_id, report_id):
    """Delete user report"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("DELETE FROM Reports WHERE id = %s AND user_id = %s", (report_id, user_id))
    deleted = cur.rowcount > 0
    conn.commit()
    cur.close()
    conn.close()
    return deleted
