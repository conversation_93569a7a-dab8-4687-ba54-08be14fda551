import json
import os
import hmac
import hashlib
import razorpay
from utils import get_user_from_token
from db_utils import update_user_tier

# Razorpay setup
RAZORPAY_KEY_ID = os.environ.get("RAZORPAY_KEY_ID")
RAZORPAY_KEY_SECRET = os.environ.get("RAZORPAY_KEY_SECRET")
razorpay_client = razorpay.Client(auth=(RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET))

def handler(event, context):
    """Verify Razorpay payment and upgrade user tier"""
    
    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    }
    
    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }
    
    if event['httpMethod'] != 'POST':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({'error': 'Method not allowed'})
        }
    
    # Get user from token
    auth_header = event.get('headers', {}).get('authorization', '')
    token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    user = get_user_from_token(token)
    
    if not user:
        return {
            'statusCode': 401,
            'headers': headers,
            'body': json.dumps({'error': 'Authentication required'})
        }
    
    try:
        body = json.loads(event['body'])
        order_id = body.get('order_id')
        payment_id = body.get('payment_id')
        signature = body.get('signature')
        tier = body.get('tier')
        
        # Validate required fields
        if not all([order_id, payment_id, signature, tier]):
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Missing payment verification data'})
            }
        
        # Verify payment signature
        generated_signature = hmac.new(
            RAZORPAY_KEY_SECRET.encode(),
            f"{order_id}|{payment_id}".encode(),
            hashlib.sha256
        ).hexdigest()
        
        if generated_signature != signature:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Payment verification failed: Invalid signature'})
            }
        
        # Fetch payment details from Razorpay
        payment = razorpay_client.payment.fetch(payment_id)
        
        if payment['status'] != 'captured':
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Payment not captured'})
            }
        
        # Update user tier
        update_user_tier(user['id'], tier)
        
        return {
            'statusCode': 200,
            'headers': headers,
            'body': json.dumps({
                'message': f'Successfully upgraded to {tier.capitalize()} tier!',
                'tier': tier
            })
        }
        
    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': headers,
            'body': json.dumps({'error': 'Invalid JSON'})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': f'Payment verification error: {str(e)}'})
        }
