import json
import os
from utils import hash_password, create_jwt_token

# Use local SQLite database for local development
try:
    if os.environ.get('SUPABASE_URL') or os.environ.get('SUPABASE_HOST'):
        from db_utils import create_user, get_user_by_email
    else:
        raise ImportError("Using local database")
except ImportError:
    from db_utils_local import create_user, get_user_by_email

def handler(event, context):
    """User signup handler"""

    # Handle CORS
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
    }

    if event['httpMethod'] == 'OPTIONS':
        return {
            'statusCode': 200,
            'headers': headers,
            'body': ''
        }

    if event['httpMethod'] != 'POST':
        return {
            'statusCode': 405,
            'headers': headers,
            'body': json.dumps({'error': 'Method not allowed'})
        }

    try:
        body = json.loads(event['body'])
        email = body.get('email', '').strip().lower()
        password = body.get('password', '')

        # Validate input
        if not email or not password:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Email and password are required'})
            }

        if len(password) < 6:
            return {
                'statusCode': 400,
                'headers': headers,
                'body': json.dumps({'error': 'Password must be at least 6 characters'})
            }

        # Check if user already exists
        existing_user = get_user_by_email(email)
        if existing_user:
            return {
                'statusCode': 409,
                'headers': headers,
                'body': json.dumps({'error': 'Email already exists'})
            }

        # Hash password and create user
        password_hash = hash_password(password)
        user_id = create_user(email, password_hash.decode('utf-8'))

        if not user_id:
            return {
                'statusCode': 500,
                'headers': headers,
                'body': json.dumps({'error': 'Failed to create user'})
            }

        # Create JWT token
        token = create_jwt_token(user_id)

        return {
            'statusCode': 201,
            'headers': headers,
            'body': json.dumps({
                'message': 'Account created successfully',
                'token': token,
                'user': {
                    'id': user_id,
                    'email': email,
                    'tier': 'free'
                }
            })
        }

    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': headers,
            'body': json.dumps({'error': 'Invalid JSON'})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': headers,
            'body': json.dumps({'error': str(e)})
        }
