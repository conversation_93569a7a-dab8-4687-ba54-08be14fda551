import os
import sqlite3
from datetime import datetime

def get_db_connection():
    """Get database connection - SQLite for local development"""
    # Use SQLite for local development
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'purpose_robot.db')
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # This makes rows behave like dictionaries
    return conn

def get_user_by_id(user_id):
    """Get user by ID"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("SELECT * FROM Users WHERE id = ?", (user_id,))
    user = cur.fetchone()
    conn.close()
    if user:
        return dict(user)
    return None

def get_user_by_email(email):
    """Get user by email"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("SELECT * FROM Users WHERE email = ?", (email,))
    user = cur.fetchone()
    conn.close()
    if user:
        return dict(user)
    return None

def create_user(email, password_hash):
    """Create new user"""
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute(
            "INSERT INTO Users (email, password_hash) VALUES (?, ?)",
            (email, password_hash)
        )
        user_id = cur.lastrowid
        conn.commit()
        return user_id
    except sqlite3.IntegrityError:
        conn.rollback()
        return None
    finally:
        conn.close()

def update_user_tier(user_id, tier):
    """Update user tier"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("UPDATE Users SET tier = ? WHERE id = ?", (tier, user_id))
    conn.commit()
    conn.close()

def reset_query_count(user_id):
    """Reset user query count"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute(
        "UPDATE Users SET query_count = 0, last_reset = ? WHERE id = ?",
        (datetime.now().isoformat(), user_id)
    )
    conn.commit()
    conn.close()

def increment_query_count(user_id):
    """Increment user query count"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("UPDATE Users SET query_count = query_count + 1 WHERE id = ?", (user_id,))
    conn.commit()
    conn.close()

def save_report(user_id, prompt, response, tier):
    """Save report to database"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute(
        "INSERT INTO Reports (user_id, prompt, response, tier, timestamp) VALUES (?, ?, ?, ?, ?)",
        (user_id, prompt, response, tier, datetime.now().isoformat())
    )
    conn.commit()
    conn.close()

def get_user_reports(user_id):
    """Get user reports"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute(
        "SELECT id, prompt as query, response FROM Reports WHERE user_id = ? ORDER BY timestamp DESC",
        (user_id,)
    )
    reports = [dict(row) for row in cur.fetchall()]
    conn.close()
    return reports

def delete_report(report_id, user_id):
    """Delete a specific report"""
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("DELETE FROM Reports WHERE id = ? AND user_id = ?", (report_id, user_id))
    deleted = cur.rowcount > 0
    conn.commit()
    conn.close()
    return deleted

def delete_user_report(user_id, report_id):
    """Delete a specific report (alternative function name)"""
    return delete_report(report_id, user_id)
