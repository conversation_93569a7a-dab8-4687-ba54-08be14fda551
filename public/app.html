<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Policy Impact Simulator</title>
    <link href="/static/styles.css" rel="stylesheet">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        .blurred-text { filter: blur(4px); }
        .tab-active { background-color: #3b82f6; color: white; }
        .tab-inactive { background-color: #e5e7eb; color: #374151; }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-white p-4 border-r">
            <h2 class="text-xl font-bold mb-4">Simulations</h2>
            <ul id="simList" class="space-y-2 text-sm mb-6">
                <!-- History will be populated here -->
            </ul>
            <button id="newSimBtn" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">+ New Simulation</button>

            <!-- User Info Section -->
            <div class="mt-6 pt-4 border-t">
                <div id="userInfo" class="text-xs text-gray-600 mb-2"></div>
                <button id="logoutBtn" class="w-full bg-red-600 text-white py-1 px-2 rounded text-xs hover:bg-red-700">Logout</button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Top Bar -->
            <div class="flex justify-between items-center mb-6">
                <div class="flex gap-2 items-center">
                    <label for="tierSelect" class="text-sm">Tier:</label>
                    <select id="tierSelect" class="border p-1 rounded">
                        <option value="free">Free</option>
                        <option value="graduate">Graduate</option>
                        <option value="masters">Masters</option>
                        <option value="phd">PhD/Think Tank</option>
                    </select>
                </div>
                <div class="flex items-center gap-4">
                    <button id="buyPremiumBtn" class="bg-green-600 text-white px-4 py-2 rounded">Buy Premium</button>
                    <span id="userInfoTop" class="text-sm text-gray-600"></span>
                </div>
            </div>

            <!-- Input Box -->
            <form id="queryForm" class="mb-4">
                <textarea id="queryInput" placeholder="Paste your policy here or describe the policy impact you want to simulate..."
                          class="w-full h-40 p-4 border rounded mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">Run Simulation</button>
            </form>

            <!-- Output Section -->
            <div id="outputSection" class="mt-8 hidden">
                <div class="mb-2 font-semibold">Your Query:</div>
                <div id="currentQuery" class="bg-white p-4 border rounded mb-4"></div>

                <!-- Tabs -->
                <div class="flex gap-4 mb-4">
                    <button id="summaryTab" class="tab-active px-4 py-1 rounded">Summary</button>
                    <button id="chartTab" class="tab-inactive px-4 py-1 rounded">Impact Chart</button>
                    <button id="timelineTab" class="tab-inactive px-4 py-1 rounded">Timeline</button>
                    <button id="sourcesTab" class="tab-inactive px-4 py-1 rounded">Sources</button>
                </div>

                <!-- Tab Content -->
                <div id="reportContent" class="bg-white p-4 border rounded min-h-[200px]">
                    <div id="summaryContent" class="tab-content">
                        <!-- Summary content will be populated here -->
                    </div>
                    <div id="chartContent" class="tab-content hidden">
                        <p class="text-gray-500">Impact charts and visualizations will be available in premium version.</p>
                    </div>
                    <div id="timelineContent" class="tab-content hidden">
                        <p class="text-gray-500">Timeline analysis will be available in premium version.</p>
                    </div>
                    <div id="sourcesContent" class="tab-content hidden">
                        <p class="text-gray-500">Source references will be available in premium version.</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-4 mt-4">
                    <button id="copyBtn" class="bg-blue-500 text-white px-4 py-2 rounded">Copy</button>
                    <button id="downloadPdfBtn" class="bg-gray-300 px-4 py-2 rounded">Download PDF</button>
                    <button id="downloadMdBtn" class="bg-gray-300 px-4 py-2 rounded">Download Markdown</button>
                </div>

                <!-- Premium Unlock -->
                <div id="premiumUnlock" class="mt-6 text-center">
                    <p class="text-sm text-gray-600">Unlock full report with references and charts</p>
                    <button id="unlockBtn" class="mt-2 bg-yellow-500 text-white px-6 py-2 rounded">Unlock Full Report</button>
                </div>
            </div>
        </main>
    </div>

    <!-- Error/Success Messages -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        // Global variables
        let currentUser = null;
        let authToken = localStorage.getItem('authToken');
        let currentActiveTab = 'summary';

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!authToken) {
                window.location.href = '/login.html';
                return;
            }
            loadUserInfo();
            loadHistory();
            initializeTabs();
            initializeEventListeners();
        });

        // Load user information
        async function loadUserInfo() {
            try {
                const response = await fetch('/.netlify/functions/index', {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.status === 401) {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login.html';
                    return;
                }

                const data = await response.json();
                currentUser = data.user;

                // Update sidebar user info
                document.getElementById('userInfo').textContent =
                    `${currentUser.email}\n${currentUser.tier.toUpperCase()} - ${currentUser.query_count} queries used`;

                // Update top bar user info
                document.getElementById('userInfoTop').textContent =
                    `${currentUser.email} (${currentUser.tier.toUpperCase()})`;

                // Update tier select to show current tier
                document.getElementById('tierSelect').value = currentUser.tier;
            } catch (error) {
                showMessage('Failed to load user info', 'error');
            }
        }

        // Initialize tabs
        function initializeTabs() {
            const tabs = ['summary', 'chart', 'timeline', 'sources'];
            tabs.forEach(tab => {
                document.getElementById(`${tab}Tab`).addEventListener('click', () => switchTab(tab));
            });
        }

        // Switch tabs
        function switchTab(tabName) {
            // Update tab buttons
            const tabs = ['summary', 'chart', 'timeline', 'sources'];
            tabs.forEach(tab => {
                const tabBtn = document.getElementById(`${tab}Tab`);
                const tabContent = document.getElementById(`${tab}Content`);

                if (tab === tabName) {
                    tabBtn.className = 'tab-active px-4 py-1 rounded';
                    tabContent.classList.remove('hidden');
                } else {
                    tabBtn.className = 'tab-inactive px-4 py-1 rounded';
                    tabContent.classList.add('hidden');
                }
            });
            currentActiveTab = tabName;
        }

        // Initialize event listeners
        function initializeEventListeners() {
            // Form submission
            document.getElementById('queryForm').addEventListener('submit', handleFormSubmission);

            // New simulation button
            document.getElementById('newSimBtn').addEventListener('click', () => {
                document.getElementById('queryInput').value = '';
                document.getElementById('outputSection').classList.add('hidden');
            });

            // Buy premium button
            document.getElementById('buyPremiumBtn').addEventListener('click', () => {
                const currentTier = currentUser.tier;
                const nextTier = currentTier === 'free' ? 'graduate' :
                                currentTier === 'graduate' ? 'masters' : 'phd';
                handleTierUpgrade(nextTier);
            });

            // Unlock button
            document.getElementById('unlockBtn').addEventListener('click', () => {
                handleTierUpgrade('masters'); // Unlock with masters tier
            });

            // Action buttons
            document.getElementById('copyBtn').addEventListener('click', copyReport);
            document.getElementById('downloadPdfBtn').addEventListener('click', () => downloadReport('pdf'));
            document.getElementById('downloadMdBtn').addEventListener('click', () => downloadReport('md'));

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', logout);
        }

        // Handle form submission
        async function handleFormSubmission(e) {
            e.preventDefault();

            const query = document.getElementById('queryInput').value.trim();
            const tier = document.getElementById('tierSelect').value;

            if (!query) {
                showMessage('Please enter a query', 'error');
                return;
            }

            // Check if tier upgrade is needed
            if (tier !== currentUser.tier) {
                await handleTierUpgrade(tier);
                return;
            }

            await submitQuery(query, tier);
        }

        // Submit query to API
        async function submitQuery(query, tier) {
            try {
                const response = await fetch('/.netlify/functions/index', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ query, tier })
                });

                const data = await response.json();

                if (!response.ok) {
                    showMessage(data.error || 'Query failed', 'error');
                    return;
                }

                // Display report in new UI
                document.getElementById('currentQuery').textContent = query;

                // Show summary content (blur for free tier)
                const summaryContent = document.getElementById('summaryContent');
                if (currentUser.tier === 'free') {
                    summaryContent.innerHTML = `<p class="blurred-text">${data.report}</p>`;
                } else {
                    summaryContent.textContent = data.report;
                }

                // Show output section
                document.getElementById('outputSection').classList.remove('hidden');

                // Switch to summary tab
                switchTab('summary');

                // Show/hide premium unlock based on tier
                const premiumUnlock = document.getElementById('premiumUnlock');
                if (currentUser.tier === 'free') {
                    premiumUnlock.classList.remove('hidden');
                } else {
                    premiumUnlock.classList.add('hidden');
                }

                // Refresh history and user info
                loadHistory();
                loadUserInfo();

                showMessage('Report generated successfully!', 'success');
            } catch (error) {
                showMessage('Network error occurred', 'error');
            }
        }

        // Handle tier upgrade
        async function handleTierUpgrade(tier) {
            if (tier === 'free') {
                showMessage('Cannot downgrade to free tier', 'error');
                return;
            }

            try {
                // Create Razorpay order
                const orderResponse = await fetch('/.netlify/functions/create_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ tier })
                });

                const orderData = await orderResponse.json();

                if (!orderResponse.ok) {
                    showMessage(orderData.error || 'Failed to create order', 'error');
                    return;
                }

                // Initialize Razorpay payment
                const options = {
                    key: orderData.key_id,
                    amount: orderData.amount,
                    currency: orderData.currency,
                    order_id: orderData.order_id,
                    name: 'Purpose Robot',
                    description: `Upgrade to ${tier} tier`,
                    handler: function(response) {
                        verifyPayment(response, tier);
                    },
                    prefill: {
                        email: currentUser.email
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            } catch (error) {
                showMessage('Payment initialization failed', 'error');
            }
        }

        // Verify payment
        async function verifyPayment(paymentResponse, tier) {
            try {
                const response = await fetch('/.netlify/functions/verify_payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        order_id: paymentResponse.razorpay_order_id,
                        payment_id: paymentResponse.razorpay_payment_id,
                        signature: paymentResponse.razorpay_signature,
                        tier: tier
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage(data.message, 'success');
                    loadUserInfo(); // Refresh user info
                } else {
                    showMessage(data.error || 'Payment verification failed', 'error');
                }
            } catch (error) {
                showMessage('Payment verification error', 'error');
            }
        }

        // Load history
        async function loadHistory() {
            try {
                const response = await fetch('/.netlify/functions/history', {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const history = await response.json();
                    displayHistory(history);
                }
            } catch (error) {
                console.error('Failed to load history:', error);
            }
        }

        // Display history in sidebar
        function displayHistory(history) {
            const simList = document.getElementById('simList');

            if (history.length === 0) {
                simList.innerHTML = '<li class="text-gray-400 text-xs">No simulations yet</li>';
                return;
            }

            simList.innerHTML = history.map(item => `
                <li class="cursor-pointer text-blue-600 hover:text-blue-800 text-xs p-2 border rounded mb-1"
                    onclick="loadSimulation('${item.query}', '${item.response.replace(/'/g, "\\'")}', ${item.id})">
                    ${item.query.length > 30 ? item.query.substring(0, 30) + '...' : item.query}
                    <button onclick="event.stopPropagation(); deleteReport(${item.id})"
                            class="float-right text-red-400 hover:text-red-600 text-xs">×</button>
                </li>
            `).join('');
        }

        // Load simulation from history
        function loadSimulation(query, response, id) {
            document.getElementById('queryInput').value = query;
            document.getElementById('currentQuery').textContent = query;

            const summaryContent = document.getElementById('summaryContent');
            if (currentUser.tier === 'free') {
                summaryContent.innerHTML = `<p class="blurred-text">${response}</p>`;
            } else {
                summaryContent.textContent = response;
            }

            document.getElementById('outputSection').classList.remove('hidden');
            switchTab('summary');

            // Show/hide premium unlock based on tier
            const premiumUnlock = document.getElementById('premiumUnlock');
            if (currentUser.tier === 'free') {
                premiumUnlock.classList.remove('hidden');
            } else {
                premiumUnlock.classList.add('hidden');
            }
        }

        // Delete report
        async function deleteReport(reportId) {
            if (!confirm('Are you sure you want to delete this report?')) return;

            try {
                const response = await fetch(`/.netlify/functions/delete_report/${reportId}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    showMessage('Report deleted', 'success');
                    loadHistory();
                } else {
                    showMessage('Failed to delete report', 'error');
                }
            } catch (error) {
                showMessage('Network error', 'error');
            }
        }

        // Copy report function
        function copyReport() {
            const summaryContent = document.getElementById('summaryContent').textContent;
            navigator.clipboard.writeText(summaryContent).then(() => {
                showMessage('Report copied to clipboard!', 'success');
            }).catch(() => {
                showMessage('Failed to copy report', 'error');
            });
        }

        // Download report function
        function downloadReport(format) {
            const summaryContent = document.getElementById('summaryContent').textContent;
            const query = document.getElementById('currentQuery').textContent;

            let content, filename, mimeType;

            if (format === 'pdf') {
                showMessage('PDF download will be available in premium version', 'error');
                return;
            } else if (format === 'md') {
                content = `# Policy Impact Simulation\n\n## Query\n${query}\n\n## Summary\n${summaryContent}`;
                filename = 'policy_simulation.md';
                mimeType = 'text/markdown';
            }

            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            window.location.href = '/login.html';
        }

        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded shadow-lg ${type === 'error' ? 'bg-red-600' : 'bg-green-600'} text-white mb-2`;
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            setTimeout(() => {
                container.removeChild(messageDiv);
            }, 5000);
        }
    </script>
</body>
</html>
