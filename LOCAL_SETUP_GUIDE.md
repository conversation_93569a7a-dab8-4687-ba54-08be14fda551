# 🚀 Local Setup Guide - Krubu Policy Impact Simulator

## 🎯 Quick Start Options

### **Option 1: Full Backend (Recommended)**
```bash
# Run the automated setup script
./start_local.sh
```

### **Option 2: Frontend Only (UI Testing)**
```bash
# Run static frontend for UI testing
./start_simple.sh
```

### **Option 3: Manual Setup**
Follow the detailed steps below for custom configuration.

---

## 🔧 Manual Setup Instructions

### **1. Prerequisites**
- Node.js (for Tailwind CSS and Netlify CLI)
- Python 3.9+ (for serverless functions)
- Git (for version control)

### **2. Install Dependencies**

```bash
# Install Node.js dependencies
npm install

# Install Netlify CLI globally
npm install -g netlify-cli

# Build Tailwind CSS
npm run build
```

### **3. Database Setup**

```bash
# Setup local database and create test user
python3 create_admin_user.py
```

This will:
- ✅ Update database schema
- ✅ Create test user: `<EMAIL>` / `test123`
- ✅ Add sample simulation data
- ✅ Set user to PhD tier (unlimited queries)

### **4. Environment Configuration**

Create a `.env` file in the project root:

```bash
# Copy the template
cp .env.example .env

# Edit with your API keys
nano .env
```

**Required Environment Variables:**
```env
# OpenAI API Key (required for AI functionality)
OPENAI_API_KEY=your_openai_api_key_here

# JWT Secret (can be any random string for local dev)
JWT_SECRET=local_development_jwt_secret_key_12345

# Razorpay Keys (optional for basic testing)
RAZORPAY_KEY_ID=your_razorpay_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here
```

### **5. Start the Application**

#### **Full Backend Mode:**
```bash
netlify dev
```
- ✅ Complete functionality
- ✅ All API endpoints working
- ✅ Database operations
- ✅ Authentication system
- ✅ Payment integration (if keys provided)

#### **Frontend Only Mode:**
```bash
python3 -m http.server 8000 --directory public
```
- ✅ UI testing
- ✅ Layout and styling
- ❌ No backend functionality

---

## 🌐 Access the Application

### **Full Backend (Netlify Dev)**
- **URL:** http://localhost:8888
- **Login:** http://localhost:8888/login.html
- **Main App:** http://localhost:8888/app.html

### **Frontend Only**
- **URL:** http://localhost:8000
- **Login:** http://localhost:8000/login.html
- **Main App:** http://localhost:8000/app.html

---

## 🔑 Test Credentials

**Email:** `<EMAIL>`  
**Password:** `test123`  
**Tier:** PhD (Unlimited queries)

---

## 📱 Features Available

### **✅ Working in Both Modes**
- Modern light-themed UI
- Sidebar navigation
- Tabbed interface (Summary/Chart/Timeline/Sources)
- Responsive design
- Copy to clipboard
- Markdown download
- Form validation

### **✅ Working Only in Full Backend Mode**
- User authentication (login/logout/signup)
- Policy simulation queries
- AI-powered report generation
- History management
- Report deletion
- Tier-based access control
- Payment integration (with API keys)

### **⚠️ Requires API Keys**
- **OpenAI API Key:** For AI-powered policy simulations
- **Razorpay Keys:** For payment processing and tier upgrades

---

## 🛠️ Troubleshooting

### **Common Issues**

#### **1. CSS Not Loading**
```bash
# Rebuild CSS
npm run build
```

#### **2. Database Errors**
```bash
# Reset database
python3 create_admin_user.py
```

#### **3. Port Already in Use**
```bash
# Kill existing processes
pkill -f "netlify dev"
pkill -f "python3 -m http.server"

# Or use different port
python3 -m http.server 8001 --directory public
```

#### **4. Netlify CLI Issues**
```bash
# Reinstall Netlify CLI
npm uninstall -g netlify-cli
npm install -g netlify-cli
```

#### **5. Function Import Errors**
The app automatically detects local vs production environment:
- **Local:** Uses SQLite database (`purpose_robot.db`)
- **Production:** Uses Supabase PostgreSQL

### **Environment Detection**
```python
# Functions check for these environment variables:
if os.environ.get('SUPABASE_URL') or os.environ.get('SUPABASE_HOST'):
    # Use Supabase (production)
else:
    # Use SQLite (local development)
```

---

## 🔍 Testing Checklist

### **Frontend Testing (Both Modes)**
- [ ] Login page loads correctly
- [ ] App redirects to login when not authenticated
- [ ] UI layout is responsive
- [ ] Sidebar shows simulation history
- [ ] Tabs switch correctly
- [ ] Copy button works
- [ ] Download markdown works

### **Backend Testing (Full Mode Only)**
- [ ] Login with test credentials works
- [ ] User info displays correctly
- [ ] Submit policy simulation query
- [ ] History loads in sidebar
- [ ] Click on history item loads simulation
- [ ] Delete report works
- [ ] Logout works

### **API Testing**
- [ ] `/.netlify/functions/login` - Authentication
- [ ] `/.netlify/functions/index` - Policy simulation
- [ ] `/.netlify/functions/history` - Get user history
- [ ] `/.netlify/functions/delete_report/{id}` - Delete report

---

## 🎉 Success Indicators

### **Frontend Working**
- ✅ Clean, modern light-themed interface
- ✅ Sidebar with simulation history
- ✅ Tabbed content area
- ✅ Responsive design
- ✅ No console errors

### **Backend Working**
- ✅ Successful login with test credentials
- ✅ Policy simulation generates reports
- ✅ History loads and displays
- ✅ All CRUD operations work
- ✅ Authentication flow complete

### **Full Integration**
- ✅ New UI + Old backend functionality
- ✅ Enhanced user experience
- ✅ Premium features visible
- ✅ All existing features preserved

---

## 📞 Need Help?

1. **Check browser console** for JavaScript errors
2. **Check terminal output** for server errors
3. **Verify credentials** are correct
4. **Ensure database** is properly set up
5. **Check environment variables** are loaded

The app is now ready for local development and testing! 🎊
