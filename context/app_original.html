<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purpose Robot - Policy Impact Simulator</title>
    <link href="/static/styles.css" rel="stylesheet">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-gray-800 p-4 border-b border-gray-700">
        <div class="max-w-6xl mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold text-blue-400">Purpose Robot</h1>
            <div class="flex items-center space-x-4">
                <span id="userInfo" class="text-gray-300"></span>
                <button id="logoutBtn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded text-sm">Logout</button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto p-6">
        <!-- Query Form -->
        <div class="bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-700 mb-6">
            <h2 class="text-xl font-bold text-blue-400 mb-4">Policy Impact Simulator</h2>
            <form id="queryForm" class="space-y-4">
                <div>
                    <label class="block text-gray-300 mb-2">Query (format: "Simulate the impact of [policy] on [sector]")</label>
                    <input type="text" id="queryInput" placeholder="e.g., Simulate the impact of carbon tax on automotive industry" 
                           class="w-full p-3 bg-gray-700 border border-gray-600 rounded text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-2">Tier</label>
                    <select id="tierSelect" class="w-full p-3 bg-gray-700 border border-gray-600 rounded text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="free">Free (5 queries/day)</option>
                        <option value="graduate">Graduate (20 queries/day) - ₹500/month</option>
                        <option value="masters">Masters (50 queries/day) - ₹1000/month</option>
                        <option value="phd">PhD (Unlimited) - ₹2000/month</option>
                    </select>
                </div>
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 p-3 rounded font-semibold">Generate Report</button>
            </form>
        </div>

        <!-- Report Display -->
        <div id="reportSection" class="bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-700 mb-6 hidden">
            <h3 class="text-lg font-bold text-blue-400 mb-4">Generated Report</h3>
            <div id="reportContent" class="text-gray-300 whitespace-pre-wrap"></div>
        </div>

        <!-- History -->
        <div class="bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-700">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-blue-400">Query History</h3>
                <button id="refreshHistory" class="bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm">Refresh</button>
            </div>
            <div id="historyList" class="space-y-3"></div>
        </div>
    </main>

    <!-- Error/Success Messages -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        // Global variables
        let currentUser = null;
        let authToken = localStorage.getItem('authToken');

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!authToken) {
                window.location.href = '/login.html';
                return;
            }
            loadUserInfo();
            loadHistory();
        });

        // Load user information
        async function loadUserInfo() {
            try {
                const response = await fetch('/.netlify/functions/index', {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                if (response.status === 401) {
                    localStorage.removeItem('authToken');
                    window.location.href = '/login.html';
                    return;
                }
                
                const data = await response.json();
                currentUser = data.user;
                
                document.getElementById('userInfo').textContent = 
                    `${currentUser.email} (${currentUser.tier.toUpperCase()}) - ${currentUser.query_count} queries used`;
                
                // Update tier select to show current tier
                document.getElementById('tierSelect').value = currentUser.tier;
            } catch (error) {
                showMessage('Failed to load user info', 'error');
            }
        }

        // Handle form submission
        document.getElementById('queryForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const query = document.getElementById('queryInput').value.trim();
            const tier = document.getElementById('tierSelect').value;
            
            if (!query) {
                showMessage('Please enter a query', 'error');
                return;
            }

            // Check if tier upgrade is needed
            if (tier !== currentUser.tier) {
                await handleTierUpgrade(tier);
                return;
            }

            await submitQuery(query, tier);
        });

        // Submit query to API
        async function submitQuery(query, tier) {
            try {
                const response = await fetch('/.netlify/functions/index', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ query, tier })
                });

                const data = await response.json();

                if (!response.ok) {
                    showMessage(data.error || 'Query failed', 'error');
                    return;
                }

                // Display report
                document.getElementById('reportContent').textContent = data.report;
                document.getElementById('reportSection').classList.remove('hidden');
                
                // Refresh history and user info
                loadHistory();
                loadUserInfo();
                
                showMessage('Report generated successfully!', 'success');
            } catch (error) {
                showMessage('Network error occurred', 'error');
            }
        }

        // Handle tier upgrade
        async function handleTierUpgrade(tier) {
            if (tier === 'free') {
                showMessage('Cannot downgrade to free tier', 'error');
                return;
            }

            try {
                // Create Razorpay order
                const orderResponse = await fetch('/.netlify/functions/create_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ tier })
                });

                const orderData = await orderResponse.json();

                if (!orderResponse.ok) {
                    showMessage(orderData.error || 'Failed to create order', 'error');
                    return;
                }

                // Initialize Razorpay payment
                const options = {
                    key: orderData.key_id,
                    amount: orderData.amount,
                    currency: orderData.currency,
                    order_id: orderData.order_id,
                    name: 'Purpose Robot',
                    description: `Upgrade to ${tier} tier`,
                    handler: function(response) {
                        verifyPayment(response, tier);
                    },
                    prefill: {
                        email: currentUser.email
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            } catch (error) {
                showMessage('Payment initialization failed', 'error');
            }
        }

        // Verify payment
        async function verifyPayment(paymentResponse, tier) {
            try {
                const response = await fetch('/.netlify/functions/verify_payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        order_id: paymentResponse.razorpay_order_id,
                        payment_id: paymentResponse.razorpay_payment_id,
                        signature: paymentResponse.razorpay_signature,
                        tier: tier
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage(data.message, 'success');
                    loadUserInfo(); // Refresh user info
                } else {
                    showMessage(data.error || 'Payment verification failed', 'error');
                }
            } catch (error) {
                showMessage('Payment verification error', 'error');
            }
        }

        // Load history
        async function loadHistory() {
            try {
                const response = await fetch('/.netlify/functions/history', {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const history = await response.json();
                    displayHistory(history);
                }
            } catch (error) {
                console.error('Failed to load history:', error);
            }
        }

        // Display history
        function displayHistory(history) {
            const historyList = document.getElementById('historyList');
            
            if (history.length === 0) {
                historyList.innerHTML = '<p class="text-gray-400">No queries yet</p>';
                return;
            }

            historyList.innerHTML = history.map(item => `
                <div class="bg-gray-700 p-4 rounded border border-gray-600">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="font-semibold text-blue-300">${item.query}</h4>
                        <button onclick="deleteReport(${item.id})" class="text-red-400 hover:text-red-300 text-sm">Delete</button>
                    </div>
                    <p class="text-gray-300 text-sm whitespace-pre-wrap">${item.response}</p>
                </div>
            `).join('');
        }

        // Delete report
        async function deleteReport(reportId) {
            if (!confirm('Are you sure you want to delete this report?')) return;

            try {
                const response = await fetch(`/.netlify/functions/delete_report/${reportId}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    showMessage('Report deleted', 'success');
                    loadHistory();
                } else {
                    showMessage('Failed to delete report', 'error');
                }
            } catch (error) {
                showMessage('Network error', 'error');
            }
        }

        // Logout
        document.getElementById('logoutBtn').addEventListener('click', function() {
            localStorage.removeItem('authToken');
            window.location.href = '/login.html';
        });

        // Refresh history
        document.getElementById('refreshHistory').addEventListener('click', loadHistory);

        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded shadow-lg ${type === 'error' ? 'bg-red-600' : 'bg-green-600'} text-white mb-2`;
            messageDiv.textContent = message;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                container.removeChild(messageDiv);
            }, 5000);
        }
    </script>
</body>
</html>
