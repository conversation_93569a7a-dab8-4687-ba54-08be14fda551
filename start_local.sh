#!/bin/bash

echo "🚀 Starting Krubu Policy Impact Simulator locally..."
echo "=================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo "📝 Creating .env file with default values..."
    
    cat > .env << EOF
# Local Development Environment Variables
# Update these with your actual API keys

# OpenAI API Key (required for AI functionality)
OPENAI_API_KEY=your_openai_api_key_here

# JWT Secret for authentication (can be any random string for local dev)
JWT_SECRET=local_development_jwt_secret_key_12345

# Razorpay Keys (for payment functionality - optional for basic testing)
RAZORPAY_KEY_ID=your_razorpay_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here

# Database Configuration
# For local development, we'll use SQLite instead of Supabase
# Comment out these Supabase variables to use local SQLite database
# SUPABASE_URL=your_supabase_connection_url
# SUPABASE_HOST=your_supabase_host
# SUPABASE_DB=your_database_name
# SUPABASE_USER=your_database_user
# SUPABASE_PASSWORD=your_database_password
# SUPABASE_PORT=5432
EOF

    echo "✅ .env file created!"
    echo "⚠️  Please edit .env file and add your OpenAI API key for full functionality"
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Build CSS
echo "🎨 Building Tailwind CSS..."
npm run build

# Check if database exists and has the right schema
echo "🗄️  Checking database..."
if [ ! -f "purpose_robot.db" ]; then
    echo "❌ Database not found! Please run the setup script first:"
    echo "   python3 create_admin_user.py"
    exit 1
fi

# Check if Netlify CLI is installed
if ! command -v netlify &> /dev/null; then
    echo "❌ Netlify CLI not found!"
    echo "📦 Installing Netlify CLI..."
    npm install -g netlify-cli
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🔑 Test Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: test123"
echo ""
echo "🌐 Starting Netlify Dev server..."
echo "   This will start the app with full backend functionality"
echo "   The app will be available at: http://localhost:8888"
echo ""
echo "📱 Features available:"
echo "   ✅ Authentication (login/logout/signup)"
echo "   ✅ Policy simulation queries"
echo "   ✅ History management"
echo "   ✅ Report deletion"
echo "   ✅ New UI with sidebar and tabs"
echo "   ⚠️  Payment integration (requires Razorpay keys)"
echo "   ⚠️  AI functionality (requires OpenAI API key)"
echo ""
echo "🚀 Starting server..."
echo "=================================================="

# Start Netlify Dev
netlify dev
