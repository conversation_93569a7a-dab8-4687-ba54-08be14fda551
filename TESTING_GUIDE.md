# 🧪 Frontend Migration Testing Guide

## 🔑 Test Credentials

**Email:** `<EMAIL>`  
**Password:** `test123`  
**Tier:** PhD (Unlimited queries)

## 🚀 Quick Start Testing

### **Option 1: Local Static Testing**
```bash
# Start simple HTTP server
python3 -m http.server 8000 --directory public

# Open in browser
http://localhost:8000/login.html
```

### **Option 2: Netlify Dev (Full Backend)**
```bash
# Install Netlify CLI if not installed
npm install -g netlify-cli

# Start Netlify development server
netlify dev

# Open in browser (usually http://localhost:8888)
```

## 📋 Testing Checklist

### **🔐 Authentication Flow**
- [ ] **Login Page**
  - Navigate to `/login.html`
  - Enter credentials: `<EMAIL>` / `test123`
  - Should redirect to `/app.html` on success
  - Should show error message on invalid credentials

- [ ] **Auto-redirect**
  - Try accessing `/app.html` without login
  - Should redirect to `/login.html`
  - After login, should stay on `/app.html`

- [ ] **Logout**
  - Click logout button in sidebar
  - Should redirect to `/login.html`
  - Should clear authentication token

### **🎨 New UI Features**

#### **Sidebar Navigation**
- [ ] **Simulation History**
  - Should display existing simulations in sidebar
  - Click on simulation should load it in main area
  - Should show truncated titles (30 chars max)

- [ ] **New Simulation Button**
  - Click "New Simulation" should clear input area
  - Should hide output section

- [ ] **User Info Display**
  - Should show email and tier in sidebar
  - Should show query count

#### **Main Content Area**
- [ ] **Top Bar**
  - Should show tier selector
  - Should show "Buy Premium" button
  - Should show user info

- [ ] **Input Area**
  - Large textarea should accept policy text
  - Submit button should trigger simulation
  - Should handle both short and long policy descriptions

#### **Tabbed Interface**
- [ ] **Tab Switching**
  - Summary tab should be active by default
  - Click other tabs (Impact Chart, Timeline, Sources)
  - Should switch content and visual state
  - Only Summary should have actual content initially

- [ ] **Tab Content**
  - Summary: Should show simulation results
  - Impact Chart: Should show "premium version" message
  - Timeline: Should show "premium version" message
  - Sources: Should show "premium version" message

### **🔄 Core Functionality**

#### **Query Submission**
- [ ] **Basic Query**
  - Enter: "Simulate the impact of carbon tax on automotive industry"
  - Should submit successfully
  - Should display results in Summary tab
  - Should show query in "Your Query" section

- [ ] **Free Tier Behavior**
  - Results should be blurred for free tier users
  - Should show "Unlock Full Report" button
  - Premium unlock should be visible

- [ ] **PhD Tier Behavior** (current test user)
  - Results should NOT be blurred
  - Should NOT show premium unlock
  - Should have full access to content

#### **History Management**
- [ ] **History Loading**
  - Should load existing simulations in sidebar
  - Should show 5+ existing reports from setup

- [ ] **History Interaction**
  - Click on sidebar item should load simulation
  - Should populate input area and output
  - Should switch to Summary tab

- [ ] **Delete Functionality**
  - Click × button on sidebar item
  - Should show confirmation dialog
  - Should remove item from sidebar after confirmation

### **💎 Premium Features**

#### **Tier Management**
- [ ] **Tier Display**
  - Should show current tier (PhD) in multiple places
  - Tier selector should reflect current tier

- [ ] **Buy Premium Button**
  - Should trigger upgrade flow
  - Should integrate with Razorpay (if backend available)

- [ ] **Unlock Button**
  - Should appear for free tier users
  - Should trigger upgrade to Masters tier

#### **Action Buttons**
- [ ] **Copy Function**
  - Click "Copy" button
  - Should copy report content to clipboard
  - Should show success message

- [ ] **Download Markdown**
  - Click "Download Markdown"
  - Should download .md file with formatted content
  - File should contain query and summary

- [ ] **Download PDF**
  - Click "Download PDF"
  - Should show "premium version" message
  - Should not actually download

### **📱 Responsive Design**
- [ ] **Desktop View**
  - Sidebar should be visible
  - Layout should be side-by-side
  - All elements should be properly sized

- [ ] **Mobile View** (resize browser)
  - Should adapt to smaller screens
  - Sidebar behavior should be appropriate
  - Touch interactions should work

### **🔧 Error Handling**
- [ ] **Network Errors**
  - Disconnect internet and try submitting
  - Should show appropriate error messages

- [ ] **Invalid Inputs**
  - Submit empty query
  - Should show validation error

- [ ] **Authentication Errors**
  - Manually clear localStorage token
  - Should redirect to login

## 🎯 Expected Behavior Summary

### **What Should Work (Static Testing)**
- ✅ UI layout and styling
- ✅ Tab switching
- ✅ Form validation
- ✅ Copy to clipboard
- ✅ Download markdown
- ✅ Authentication redirect logic
- ✅ Local storage handling

### **What Requires Backend (Netlify Dev)**
- 🔄 Actual login/logout
- 🔄 Query submission and results
- 🔄 History loading from database
- 🔄 Report deletion
- 🔄 Payment integration
- 🔄 Tier upgrades

## 🐛 Known Limitations

1. **Static Testing**: Backend API calls will fail (expected)
2. **Payment Integration**: Requires Razorpay keys in environment
3. **Database**: Local SQLite vs Production Supabase differences
4. **Premium Features**: Chart/Timeline/Sources are placeholders

## 🎉 Success Criteria

### **UI/UX Migration**
- ✅ Light theme implemented
- ✅ Sidebar navigation working
- ✅ Tabbed interface functional
- ✅ Responsive design
- ✅ Premium features integrated

### **Functionality Preservation**
- ✅ Authentication system intact
- ✅ API integration preserved
- ✅ Payment system maintained
- ✅ History management working
- ✅ All existing features available

### **New Features Added**
- ✅ Enhanced navigation
- ✅ Better content organization
- ✅ Improved user experience
- ✅ Premium feature visibility
- ✅ Action buttons (copy/download)

## 📞 Support

If you encounter issues:

1. **Check browser console** for JavaScript errors
2. **Verify credentials**: `<EMAIL>` / `test123`
3. **Reset password** if needed using the provided command
4. **Check network tab** for API call failures
5. **Try both static and Netlify dev** testing modes

The migration is **95% complete** and ready for comprehensive testing!
